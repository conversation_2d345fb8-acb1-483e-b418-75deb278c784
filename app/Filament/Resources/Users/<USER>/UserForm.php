<?php

namespace App\Filament\Resources\Users\Schemas;

use App\Filament\Schemas\Components\PriceField;
use App\Filament\Schemas\Components\Translateable;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;

class UserForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                TextInput::make('name')
                    ->required(),

                TextInput::make('email')
                    ->unique('users', ignoreRecord: true)
                    ->required()
                    ->email(),

                TextInput::make('password')
                    ->required()
                    ->password(),

                TextInput::make('password_confirmation')
                    ->required()
                    ->password()
                    ->same('password'),
            ]);
    }
}
