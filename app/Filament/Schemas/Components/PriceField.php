<?php

namespace App\Filament\Schemas\Components;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Component;
use Filament\Schemas\Components\FusedGroup;

class PriceField extends Component
{
    protected string $view = 'filament.schemas.components.price-field';

    protected string | \Closure $priceFieldName = 'price';

    protected string | \Closure $currencyFieldName = 'currency';

    protected string | \Closure $priceFieldLabel = 'Price';

    protected string | \Closure $currencyFieldLabel = 'Currency';

    protected bool | \Closure $required = false;

    public static function make(): static
    {
        return app(static::class)
            ->schema(function (Component|PriceField $component) {
                return [
                    FusedGroup::make([
                        TextInput::make($component->getPriceFieldName())
                            ->label($component->getPriceFieldLabel())
                            ->required($component->isRequired())
                            ->numeric()
                            ->minValue(0)
                            ->columnSpan(2),

                        Select::make($component->getCurrencyFieldName())
                            ->label($component->getCurrencyFieldLabel())
                            ->searchable()
                            ->options([
                                'EGP' => 'EGP',
                                'USD' => 'USD',
                                'EUR' => 'EUR',
                                'GBP' => 'GBP',
                            ])
                            ->default('EGP')
                            ->required($component->isRequired()),
                    ])
                        ->label($component->getPriceFieldLabel())
                        ->columns(3)
                ];
            });
    }

    public function priceFieldName(string | \Closure $priceFieldName): static
    {
        $this->priceFieldName = $priceFieldName;

        return $this;
    }

    public function currencyFieldName(string | \Closure $currencyFieldName): static
    {
        $this->currencyFieldName = $currencyFieldName;

        return $this;
    }

    public function priceFieldLabel(string | \Closure $priceFieldLabel): static
    {
        $this->priceFieldLabel = $priceFieldLabel;

        return $this;
    }

    public function currencyFieldLabel(string | \Closure $currencyFieldLabel): static
    {
        $this->currencyFieldLabel = $currencyFieldLabel;

        return $this;
    }

    public function required(bool | \Closure $required = true): static
    {
        $this->required = $required;

        return $this;
    }

    public function getPriceFieldName(): string
    {
        return $this->evaluate($this->priceFieldName);
    }

    public function getCurrencyFieldName(): string
    {
        return $this->evaluate($this->currencyFieldName);
    }

    public function getPriceFieldLabel(): string
    {
        return $this->evaluate($this->priceFieldLabel);
    }

    public function getCurrencyFieldLabel(): string
    {
        return $this->evaluate($this->currencyFieldLabel);
    }

    public function isRequired(): bool
    {
        return $this->evaluate($this->required);
    }
}
