<?php

namespace App\Filament\Schemas\Components;

use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Component;

class Translateable extends Component
{
    protected string $view = 'filament.schemas.components.translateable';

    protected array | \Closure $locales = ['en', 'ar'];

    protected string | \Closure $name = '';

    protected string | \Closure $label = '';

    protected bool | \Closure $required = false;

    public static function make($name = ''): static
    {
        return app(static::class)
            ->name($name)
            ->schema(function (Translateable $component) {
                $locales = $component->getLocales();

                return collect($locales)->map(function (string $locale) use ($component) {
                    return TextInput::make($component->getName() . '.' . $locale)
                        ->label($component->getLabel() . ' (' . strtoupper($locale) . ')')
                        ->required($component->isRequired());
                })->toArray();
            });
    }

    public function locales(array | \Closure $locales): static
    {
        $this->locales = $locales;

        return $this;
    }

    public function name(string | \Closure $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function label(string | \Closure $label): static
    {
        $this->label = $label;

        return $this;
    }

    public function required(bool | \Closure $required = true): static
    {
        $this->required = $required;

        return $this;
    }

    public function getLocales(): array
    {
        return $this->evaluate($this->locales);
    }

    public function getName(): string
    {
        return $this->evaluate($this->name);
    }

    public function isRequired(): bool
    {
        return $this->evaluate($this->required);
    }

    public function getLabel(): string
    {
        return $this->evaluate($this->label);
    }
}
