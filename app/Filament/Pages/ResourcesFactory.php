<?php

namespace App\Filament\Pages;

use BackedEnum;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;
use Filament\Schemas\Components\Group;
use Filament\Support\Icons\Heroicon;

class ResourcesFactory extends Page implements HasForms, HasActions
{
    use InteractsWithForms, InteractsWithActions;

    public $data = [];

    protected string $view = 'filament.pages.resources-factory';

    protected static string | BackedEnum | null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static bool $shouldRegisterNavigation = true;

    public function mount(): void
    {
        $this->form->fill();
    }

    protected function getFormSchema(): array
    {
        return [
            Repeater::make('resources')
                ->schema([
                    TextInput::make('name')
                        ->translateLabel()
                        ->required(),

                    TextInput::make('navigation_group')
                        ->translateLabel(),

                    Repeater::make('fields')
                        ->translateLabel()
                        ->columns(4)
                        ->schema([
                            TextInput::make('name')
                                ->translateLabel()
                                ->required(),

                            Select::make('type')
                                ->options([
                                    'string' => 'String',
                                    'text' => 'Text',
                                    'integer' => 'Integer',
                                    'double' => 'Double',
                                    'boolean' => 'Boolean',
                                    'date' => 'Date',
                                    'datetime' => 'Datetime',
                                    'foreignId' => 'Foreign ID',
                                ])
                                ->translateLabel()
                                ->required(),

                            Select::make('field')
                                ->options([
                                    'TextInput' => 'Text Input',
                                    'Select' => 'Select',
                                    'Checkbox' => 'Checkbox',
                                    'Toggle' => 'Toggle',
                                    'CheckboxList' => 'Checkbox List',
                                    'Radio' => 'Radio',
                                    'DateTimePicker' => 'Date-Time Picker',
                                    'FileUpload' => 'File Upload',
                                    'RichEditor' => 'Rich Editor',
                                    'MarkdownEditor' => 'Markdown Editor',
                                    'Repeater' => 'Repeater',
                                    'Builder' => 'Builder',
                                    'TagsInput' => 'Tags Input',
                                    'Textarea' => 'Textarea',
                                    'KeyValue' => 'Key-Value',
                                    'ColorPicker' => 'Color Picker',
                                    'ToggleButtons' => 'Toggle Buttons',
                                    'Slider' => 'Slider',
                                    'CodeEditor' => 'Code Editor',
                                    'Hidden' => 'Hidden',
                                ])
                                ->translateLabel()
                                ->required(),

                            Select::make('repeater_type')
                                ->options([
                                    'normal_repeater' => 'Normal Repeater',
                                    'table_repeater' => 'Table Repeater',
                                ])
                                ->translateLabel(),

                            Group::make([
                                Toggle::make('required')
                                    ->default(true)
                                    ->translateLabel(),

                                Toggle::make('use_in_table')
                                    ->default(true)
                                    ->translateLabel(),

                                Toggle::make('multiple')
                                    ->translateLabel(),

                                Toggle::make('searchable')
                                    ->translateLabel(),

                                Toggle::make('preload')
                                    ->translateLabel(),

                                // translateable
                                Toggle::make('translateable')
                                    ->translateLabel(),

                                // priceable
                                Toggle::make('priceable')
                                    ->translateLabel(),
                            ])->columnSpan(4)
                                ->columns(5),

                            Textarea::make('instructions')
                                ->translateLabel()
                                ->columnSpan(4),
                        ]),

                    // relations repeater
                    Repeater::make('relations')
                        ->translateLabel()
                        ->columns(5)
                        ->schema([
                            TextInput::make('name')
                                ->translateLabel()
                                ->required(),

                            Select::make('type')
                                ->options([
                                    'belongsTo' => 'Belongs To',
                                    'hasOne' => 'Has One',
                                    'hasMany' => 'Has Many',
                                    'belongsToMany' => 'Belongs To Many',
                                    'morphTo' => 'Morph To',
                                    'morphMany' => 'Morph Many',
                                    'morphToMany' => 'Morph To Many',
                                ])
                                ->translateLabel()
                                ->required(),

                            TextInput::make('related_model')
                                ->translateLabel()
                                ->required(),

                            Select::make('form_field')
                                ->options([
                                    'select' => 'Select',
                                    'repeater' => 'Repeater',
                                    'relation_manager' => 'Relation Manager',
                                ])
                                ->translateLabel()
                                ->required(),

                            Select::make('repeater_type')
                                ->options([
                                    'normal_repeater' => 'Normal Repeater',
                                    'table_repeater' => 'Table Repeater',
                                ])
                                ->translateLabel(),

                            Textarea::make('instructions')
                                ->translateLabel()
                                ->columnSpan(5),
                        ]),
                ])
        ];
    }

    // statePath
    public function getFormStatePath()
    {
        return 'data';
    }

    public function generate(): void
    {
        $state = $this->form->getState();

        // save the state to json file
        file_put_contents(base_path('resources.json'), json_encode($state));
    }
}
