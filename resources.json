{"resources": [{"name": "Supplier", "navigation_group": "Pur<PERSON>.asing", "fields": [{"name": "name", "type": "string", "field": "TextInput", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "email", "type": "string", "field": "TextInput", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "phone", "type": "string", "field": "TextInput", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "address", "type": "text", "field": "Textarea", "repeater_type": null, "required": false, "use_in_table": false, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}], "relations": [{"name": "purchaseOrders", "type": "hasMany", "related_model": "PurchaseOrder", "form_field": "relation_manager", "repeater_type": null, "instructions": null}, {"name": "products", "type": "belongsToMany", "related_model": "Product", "form_field": "select", "repeater_type": null, "instructions": null}]}, {"name": "PurchaseOrder", "navigation_group": "Purchasing", "fields": [{"name": "supplier_id", "type": "foreignId", "field": "Select", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}], "relations": [{"name": "supplier", "type": "belongsTo", "related_model": "Supplier", "form_field": "select", "repeater_type": null, "instructions": null}, {"name": "purchaseOrderProducts", "type": "hasMany", "related_model": "PurchaseOrderProduct", "form_field": "repeater", "repeater_type": "table_repeater", "instructions": null}]}, {"name": "PurchaseOrderProduct", "navigation_group": "Purchasing", "fields": [{"name": "product_id", "type": "foreignId", "field": "Select", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "quantity", "type": "double", "field": "TextInput", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "buy_price", "type": "double", "field": "TextInput", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": true, "instructions": null}], "relations": []}]}